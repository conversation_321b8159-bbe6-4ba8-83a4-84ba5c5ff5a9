# This `.xcode.env` file is versioned and is used to source the environment
# used when running script phases inside Xcode.
# To customize your local environment, you can create an `.xcode.env.local`
# file that is not versioned.

# NODE_BINARY variable contains the PATH to the node executable.
#
# Customize the NODE_BINARY variable here.
# For example, to use nvm with brew, add the following line
# . "$(brew --prefix nvm)/nvm.sh" --no-use
export NODE_BINARY=$(command -v node)

# Disable Sentry source map auto upload to prevent build errors
export SENTRY_DISABLE_AUTO_UPLOAD=true

# Disable source maps in production builds to prevent module errors
export SOURCEMAP_DISABLE=true

# Set React Native environment
export RN_SRC_EXT=js,jsx,ts,tsx
