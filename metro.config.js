const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const {withSentryConfig} = require('@sentry/react-native/metro');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resetCache: true,
  resolver: {
    sourceExts: ['js', 'jsx', 'ts', 'tsx', 'json'],
    assetExts: [
      'png',
      'jpg',
      'jpeg',
      'gif',
      'svg',
      'ttf',
      'otf',
      'mp4',
      'webm',
      'wav',
      'mp3',
      'm4a',
      'aac',
      'oga',
    ],
  },
  transformer: {
    babelTransformerPath: require.resolve('@react-native/metro-babel-transformer'),
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};

// Conditionally apply Sentry config only if not disabled
const sentryDisabled = process.env.SENTRY_DISABLE_AUTO_UPLOAD === 'true';

module.exports = sentryDisabled
  ? mergeConfig(getDefaultConfig(__dirname), config)
  : withSentryConfig(mergeConfig(getDefaultConfig(__dirname), config));
