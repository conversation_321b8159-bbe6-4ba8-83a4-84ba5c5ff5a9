import React from 'react';
import {
  createGroupChannelFragment,
  GroupChannelMessageRenderer,
  useSendbirdChat,
} from '@sendbird/uikit-react-native';
import {useGroupChannel} from '@sendbird/uikit-chat-hooks';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import {KeyboardAvoidingView} from 'react-native-keyboard-controller';

import {View, Platform, Linking, ViewStyle, TouchableOpacity} from 'react-native';
import MapView, {Marker} from 'react-native-maps';
type NavigationProp = StackNavigationProp<RootStackParamList, 'SendBird'>;

const SendBird = ({route}: {route: any}) => {
  const GroupChannelFragment = createGroupChannelFragment();
  // const GroupChannelFragment = createGroupChannelFragment({Input: MyCustomInput});

  const {channelUrl, group_id} = route.params;
  const {sdk} = useSendbirdChat();
  const {channel} = useGroupChannel(sdk, channelUrl);
  console.log('🚀 ~ SendBird ~ channel:', channel);
  const navigation = useNavigation<NavigationProp>();
  if (!channel) return null;

  function openExternalMap(lat: number, lng: number) {
    const scheme = Platform.OS === 'ios' ? 'comgooglemaps://' : 'geo:';
    const fallback =
      Platform.OS === 'ios'
        ? `http://maps.apple.com/?ll=${lat},${lng}`
        : `geo:${lat},${lng}?q=${lat},${lng}`;
    const googleMapsUrl =
      Platform.OS === 'ios'
        ? `comgooglemaps://?q=${lat},${lng}`
        : `geo:${lat},${lng}?q=${lat},${lng}`;

    Linking.canOpenURL(scheme)
      .then(supported => {
        const url = supported ? googleMapsUrl : fallback;
        console.log('🚀 ~ openExternalMap ~ url:', url);
        Linking.openURL(url).catch(err => {
          console.error('🚀 ~ openExternalMap ~ open error:', err);
        });
      })
      .catch(err => {
        console.error('🚀 ~ openExternalMap ~ check error:', err);
      });
  }

  return (
    <KeyboardAvoidingView behavior={'padding'} style={{flex: 1}}>
      <GroupChannelFragment
        channel={channel}
        onPressHeaderLeft={() => navigation.goBack()}
        onPressHeaderRight={() =>
          navigation.navigate('GroupSetting', {
            channelUrl: channelUrl,
            group_id,
          })
        }
        onChannelDeleted={() => {
          // if the channel gets deleted, handle it
        }}
        renderMessage={props => {
          if (
            props &&
            props.message &&
            props.message.customType === 'location' &&
            props.message.message
          ) {
            console.log('🚀 ~ SendBird ~ props:', props);
            const [lat, lng] = props.message.message.split(',').map(Number);
            if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
              return null; // Don't render MapView if coordinates are invalid
            }
            const isSender = props.message.sender?.userId === sdk.currentUser?.userId;
            // Bubble style
            const bubbleStyle = {
              backgroundColor: isSender ? '#2563eb' : '#2d2d2d', // blue for sender, dark gray for receiver
              alignSelf: (isSender ? 'flex-end' : 'flex-start') as ViewStyle['alignSelf'],
              borderRadius: 18,
              marginVertical: 4,
              marginHorizontal: 8,
              padding: 2,
              // Optional: shadow for iOS, elevation for Android
              shadowColor: '#000',
              shadowOffset: {width: 0, height: 2},
              shadowOpacity: 0.2,
              shadowRadius: 2,
              elevation: 2,
            };
            return (
              <TouchableOpacity style={bubbleStyle} onPress={() => openExternalMap(lat, lng)}>
                <View style={{borderRadius: 14, overflow: 'hidden', width: 220, height: 140}}>
                  <MapView
                    style={{width: 220, height: 140}}
                    initialRegion={{
                      latitude: lat,
                      longitude: lng,
                      latitudeDelta: 0.01,
                      longitudeDelta: 0.01,
                    }}
                    scrollEnabled={false}
                    zoomEnabled={false}
                    pointerEvents={isSender ? 'none' : 'auto'}>
                    <Marker coordinate={{latitude: lat, longitude: lng}} />
                  </MapView>
                </View>
              </TouchableOpacity>
            );
          }
          return <GroupChannelMessageRenderer {...props} />;
        }}
      />
    </KeyboardAvoidingView>
  );
};

export default SendBird;
