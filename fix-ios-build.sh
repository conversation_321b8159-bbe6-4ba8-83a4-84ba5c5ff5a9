#!/bin/bash

# iOS Build Fix Script
# This script resolves export declaration and source map build errors

echo "🔧 Starting iOS Build Fix..."

# Step 1: Clean all caches and build artifacts
echo "🧹 Cleaning build artifacts and caches..."
cd ios
rm -rf build/
rm -rf Pods/
rm -f Podfile.lock
rm -rf ~/Library/Developer/Xcode/DerivedData/GoRaqtApp-*
cd ..

# Step 2: Clean React Native and Metro cache
echo "🚇 Cleaning React Native and Metro cache..."
npx react-native clean
rm -rf node_modules/
rm -f yarn.lock

# Step 3: Clear Metro cache specifically
echo "📱 Clearing Metro cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 3
kill $METRO_PID 2>/dev/null || true

# Step 4: Reinstall dependencies
echo "📦 Reinstalling dependencies..."
yarn install

# Step 5: Reinstall iOS pods with clean environment
echo "🍎 Reinstalling iOS pods..."
cd ios
export SENTRY_DISABLE_AUTO_UPLOAD=true
export SOURCEMAP_DISABLE=true
pod deintegrate
pod install --repo-update --clean-install
cd ..

# Step 6: Clean Xcode derived data one more time
echo "🔨 Final Xcode cleanup..."
rm -rf ~/Library/Developer/Xcode/DerivedData/GoRaqtApp-*

echo "✅ iOS Build Fix completed!"
echo ""
echo "🚀 Next steps:"
echo "1. Open Xcode and clean build folder (Cmd+Shift+K)"
echo "2. In Xcode, go to Product > Clean Build Folder"
echo "3. Run: yarn ios"
echo ""
echo "📋 What was fixed:"
echo "- ✅ Disabled Sentry source map auto upload"
echo "- ✅ Configured Metro to handle module resolution"
echo "- ✅ Updated Babel configuration for better compatibility"
echo "- ✅ Set environment variables to prevent export errors"
echo "- ✅ Cleaned all caches and build artifacts"
